<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Tournament Predictions</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>VALORANT <span class="accent">PREDICTIONS</span></h1>
                </div>

                <!-- Desktop Navigation -->
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link active">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="leaderboard.html" class="nav-link">Leaderboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.html" class="nav-link">Profile</a>
                    </li>
                </ul>

                <!-- Mobile Menu Toggle (Hidden on mobile, shown on tablet) -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" style="display: none;">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>

                <div class="user-info">
                    <div class="user-details">
                        <span id="user-points">Points: 0</span>
                        <span id="user-id-display"></span>
                    </div>
                    <button id="nav-logout-btn" class="btn btn-danger btn-sm" style="display: none;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>

            <!-- Mobile Bottom Navigation -->
            <div class="mobile-bottom-nav" id="mobile-bottom-nav">
                <div class="bottom-nav-container">
                    <a href="index.html" class="bottom-nav-item active" data-page="home">
                        <div class="nav-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="nav-label">Home</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="leaderboard.html" class="bottom-nav-item" data-page="leaderboard">
                        <div class="nav-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <span class="nav-label">Leaderboard</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="profile.html" class="bottom-nav-item" data-page="profile">
                        <div class="nav-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="nav-label">Profile</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <button class="bottom-nav-item" id="quick-actions-btn" data-page="actions">
                        <div class="nav-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <span class="nav-label">Actions</span>
                        <div class="nav-indicator"></div>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Global Prediction Status Bar -->
    <div id="prediction-status-bar" class="prediction-status-bar" style="display: none;">
        <div class="status-bar-content">
            <div class="status-message">
                <i class="fas fa-clock"></i>
                <span id="status-text">Prediction window closing soon!</span>
            </div>
            <div class="status-timer">
                <span id="global-countdown">--:--</span>
            </div>
        </div>
    </div>

    <!-- Pull to Refresh Indicator -->
    <div class="pull-to-refresh" id="pull-to-refresh">
        <div class="refresh-icon">
            <i class="fas fa-sync-alt"></i>
        </div>
        <span class="refresh-text">Pull to refresh</span>
    </div>

    <main class="main-content" id="main-content">
        <div class="container mobile-container">
            <section class="hero">
                <div class="hero-content">
                    <h1>PREDICT <span class="accent">TOURNAMENT</span> WINNERS</h1>
                    <p>Make your predictions and climb the leaderboard</p>
                    <div class="hero-actions">
                        <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer" class="btn btn-youtube">
                            <i class="fab fa-youtube"></i>
                            Click to Watch
                        </a>
                    </div>
                </div>
            </section>

            <section class="user-setup" id="auth-section">
                <div class="setup-card">
                    <h2 id="auth-title">Welcome to Valorant Predictions</h2>

                    <!-- Login Form -->
                    <div id="login-form" class="auth-form">
                        <div class="input-group">
                            <input type="email" id="login-email" placeholder="Email Address" required>
                        </div>
                        <div class="input-group">
                            <input type="password" id="login-password" placeholder="Password" required>
                        </div>
                        <div class="input-group">
                            <button id="login-btn" class="btn btn-primary">Login</button>
                        </div>
                        <p class="auth-switch">
                            Don't have an account?
                            <a href="#" id="show-register">Register here</a>
                        </p>
                    </div>

                    <!-- Registration Form -->
                    <div id="register-form" class="auth-form" style="display: none;">
                        <div class="input-group">
                            <input type="text" id="register-fullname" placeholder="Full Name" required>
                        </div>
                        <div class="input-group">
                            <input type="email" id="register-email" placeholder="Email Address" required>
                        </div>
                        <div class="input-group">
                            <input type="text" id="register-ingame" placeholder="In-Game Name (Valorant)" required>
                        </div>
                        <div class="input-group">
                            <input type="password" id="register-password" placeholder="Password" required>
                        </div>
                        <div class="input-group">
                            <button id="register-btn" class="btn btn-primary">Register</button>
                        </div>
                        <p class="auth-switch">
                            Already have an account?
                            <a href="#" id="show-login">Login here</a>
                        </p>
                    </div>
                </div>
            </section>

            <section class="matches-section">
                <div class="section-header">
                    <h2 class="section-title">ACTIVE MATCHES</h2>
                    <button class="refresh-btn mobile-refresh" id="mobile-refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div id="loading" class="loading mobile-loading">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <span>Loading matches...</span>
                </div>
                <div id="matches-container" class="matches-container mobile-matches">
                    <!-- Matches will be loaded here -->
                </div>
                <div id="no-matches" class="no-matches mobile-no-matches" style="display: none;">
                    <div class="empty-state-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <h3>No active matches</h3>
                    <p>Check back later for upcoming tournaments!</p>
                    <button class="btn btn-primary" onclick="loadMatches()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Predictions. All rights reserved.</p>
        </div>
    </footer>

    <!-- Quick Actions Modal -->
    <div id="quick-actions-modal" class="modal mobile-modal">
        <div class="modal-content mobile-modal-content">
            <div class="modal-header mobile-modal-header">
                <h2>Quick Actions</h2>
                <button class="close-btn" id="close-quick-actions">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body mobile-modal-body">
                <div class="quick-actions-grid">
                    <button class="quick-action-btn" onclick="loadMatches()">
                        <i class="fas fa-sync-alt"></i>
                        <span>Refresh Matches</span>
                    </button>
                    <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" class="quick-action-btn">
                        <i class="fab fa-youtube"></i>
                        <span>Watch Live</span>
                    </a>
                    <button class="quick-action-btn" onclick="refreshUserStats()">
                        <i class="fas fa-user-sync"></i>
                        <span>Refresh Stats</span>
                    </button>
                    <button class="quick-action-btn" onclick="window.location.href='leaderboard.html'">
                        <i class="fas fa-trophy"></i>
                        <span>Leaderboard</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Prediction Modal -->
    <div id="prediction-modal" class="modal mobile-modal">
        <div class="modal-content mobile-modal-content">
            <div class="modal-header mobile-modal-header">
                <h2>Make Your Prediction</h2>
                <button class="close-btn" id="close-prediction-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body mobile-modal-body">
                <div class="match-info">
                    <div class="teams">
                        <div class="team team-a" id="modal-team-a">
                            <div class="team-name"></div>
                            <button class="predict-btn" data-team="a">
                                <i class="fas fa-trophy"></i>
                                Predict Win
                            </button>
                        </div>
                        <div class="vs">VS</div>
                        <div class="team team-b" id="modal-team-b">
                            <div class="team-name"></div>
                            <button class="predict-btn" data-team="b">
                                <i class="fas fa-trophy"></i>
                                Predict Win
                            </button>
                        </div>
                    </div>
                </div>
                <div class="countdown-info">
                    <p>Prediction window closes in:</p>
                    <div id="modal-countdown" class="countdown"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
