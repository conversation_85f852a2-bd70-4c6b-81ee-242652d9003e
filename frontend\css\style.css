/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #0F1419;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    --primary-red: #FF4655;
    --dark-bg: #0F1419;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --accent-blue: #00D4FF;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.accent {
    color: var(--primary-red);
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-red);
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
}

.nav-logo .accent {
    color: var(--primary-red);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-red);
    background: rgba(255, 70, 85, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

/* ===== PREDICTION STATUS BAR ===== */
.prediction-status-bar {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    z-index: 999;
    box-shadow: 0 2px 10px rgba(255, 70, 85, 0.3);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.status-bar-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-timer {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    background: var(--dark-bg);
    padding: 2rem 0;
}

.main-content.with-status-bar {
    margin-top: 130px; /* 70px navbar + 60px status bar */
}

/* ===== HERO SECTION ===== */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--card-bg) 100%);
    position: relative;
    margin-bottom: 3rem;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.hero h1 .accent {
    color: var(--primary-red);
}

.hero p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 2rem;
}

.hero-actions {
    margin-top: 2rem;
}

.btn-youtube {
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-youtube:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
    background: linear-gradient(135deg, #e63946, #d32f2f);
}

.btn-youtube i {
    font-size: 1.2rem;
}

/* ===== USER SETUP ===== */
.user-setup {
    margin-bottom: 3rem;
}

.setup-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.setup-card h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.input-group input {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-red);
}

.input-group input::placeholder {
    color: var(--text-secondary);
}

/* ===== AUTHENTICATION FORMS ===== */
.auth-form {
    width: 100%;
}

.auth-switch {
    text-align: center;
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.auth-switch a {
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* ===== BUTTONS ===== */
.btn {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--primary-red);
}

.btn-primary:hover {
    background: #e63946;
}

.btn-secondary {
    background: var(--accent-blue);
    color: var(--dark-bg);
}

.btn-secondary:hover {
    background: #00b8d4;
}

.btn-success {
    background: var(--success-green);
    color: var(--dark-bg);
}

.btn-success:hover {
    background: #00d4aa;
}

.btn-danger {
    background: transparent;
    border: 1px solid var(--primary-red);
    color: var(--primary-red);
}

.btn-danger:hover {
    background: var(--primary-red);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* ===== MATCHES SECTION ===== */
.matches-section {
    padding: 3rem 0;
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.match-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.match-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid;
}

.status-open {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border-color: var(--success-green);
    animation: pulse 2s infinite;
}

.status-closed {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

.status-finished {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
}

.status-created {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.status-closed {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 2rem 0;
    text-align: center;
}

.team {
    flex: 1;
}

.team-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.vs {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-red);
    margin: 0 2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.countdown {
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    margin: 1rem 0;
}

.countdown-timer {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    font-family: 'Courier New', monospace;
}

.predict-btn {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
    width: 100%;
    margin-top: 0.5rem;
}

.predict-btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.predict-btn:disabled {
    background: var(--border-color);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.match-actions {
    margin-top: 1.5rem;
    text-align: center;
}

/* ===== LIVE MATCH STYLES ===== */
.live-indicator {
    text-align: center;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 8px;
}

.live-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-red);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    animation: livePulse 2s infinite;
}

.live-badge i {
    font-size: 0.7rem;
    animation: liveBlink 1s infinite;
}

@keyframes livePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes liveBlink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.live-indicator p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
    font-weight: 500;
}

.btn-live {
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
    animation: liveButtonPulse 3s infinite;
}

.btn-live:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
    background: linear-gradient(135deg, #e63946, #d32f2f);
    text-decoration: none;
    color: white;
}

.btn-live i {
    font-size: 1.1rem;
}

@keyframes liveButtonPulse {
    0% { box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(255, 70, 85, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3); }
}

/* ===== LOADING & EMPTY STATES ===== */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading i {
    font-size: 3rem;
    color: var(--primary-red);
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-matches {
    text-align: center;
    padding: 4rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin: 2rem 0;
}

.no-matches i {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-matches h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.no-matches p {
    color: var(--text-secondary);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.modal-header {
    background: var(--dark-bg);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    position: relative;
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-body {
    padding: 2rem;
    color: var(--text-primary);
}

.close {
    color: var(--text-secondary);
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--primary-red);
}

.countdown-info {
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 6px;
    padding: 1.5rem;
    text-align: center;
    margin-top: 2rem;
}

.countdown-info p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* ===== FOOTER ===== */
footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}

footer p {
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== LEADERBOARD STYLES ===== */
.leaderboard-section {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 0;
}

.leaderboard-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.leaderboard-header h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.leaderboard-table {
    width: 100%;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px 100px;
    gap: 1rem;
    padding: 1rem;
    align-items: center;
}

.table-header {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.table-row {
    background: rgba(30, 35, 40, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    color: var(--text-primary);
    font-weight: 500;
}

.table-row:hover {
    background: rgba(255, 70, 85, 0.1);
    border-color: var(--primary-red);
    transform: translateX(3px);
}

.table-row.current-user {
    background: rgba(255, 70, 85, 0.2);
    border-color: var(--primary-red);
}

.table-row.rank-1 {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: #ffd700;
}

.table-row.rank-2 {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
    border-color: #c0c0c0;
}

.table-row.rank-3 {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
    border-color: #cd7f32;
}

.rank {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
}

.rank i.gold {
    color: #ffd700;
}

.rank i.silver {
    color: #c0c0c0;
}

.rank i.bronze {
    color: #cd7f32;
}

.you-badge {
    background: var(--primary-red);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.username {
    font-weight: 600;
}

.points strong {
    color: var(--primary-red);
    font-size: 1.1rem;
}

/* ===== PROFILE STYLES ===== */
.profile-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.profile-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    margin-bottom: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-red);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.predictions-history {
    margin-top: 2rem;
}

.predictions-history h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: border-color 0.3s ease;
}

.prediction-item:hover {
    border-color: var(--primary-red);
}

.prediction-match {
    flex: 1;
}

.prediction-result {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.result-correct {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

.result-incorrect {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border: 1px solid var(--primary-red);
}

.result-pending {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        height: auto;
        padding: 1rem 20px;
    }

    .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .user-info {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        width: 100%;
    }

    .user-details {
        align-items: center;
        text-align: center;
    }

    .btn-sm {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        min-height: 44px; /* Touch-friendly */
    }

    /* Status bar mobile adjustments */
    .status-bar-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
        padding: 0.75rem 20px;
    }

    .status-message {
        font-size: 0.9rem;
    }

    .status-timer {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }

    .main-content {
        margin-top: 140px; /* Account for taller mobile nav */
    }

    .main-content.with-status-bar {
        margin-top: 200px; /* Account for nav + status bar */
    }

    .hero {
        padding: 2rem 0;
    }

    .hero h1 {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .btn-youtube {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-height: 44px; /* Touch-friendly */
    }

    .matches-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .input-group {
        flex-direction: column;
    }

    .teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs {
        margin: 0;
        font-size: 1rem;
    }

    .predict-btn {
        min-height: 44px; /* Touch-friendly */
        font-size: 1rem;
    }

    .btn-live {
        padding: 1rem 1.5rem;
        font-size: 0.95rem;
        min-height: 44px; /* Touch-friendly */
    }

    .live-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .live-indicator {
        margin: 1rem 0;
        padding: 0.75rem;
    }

    .table-header,
    .table-row {
        grid-template-columns: 50px 1fr 80px 80px 80px;
        gap: 0.5rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .leaderboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .prediction-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .match-card {
        padding: 1.5rem;
    }

    .modal-content {
        margin: 5% auto;
        width: 95%;
        max-width: none;
    }

    .modal-header {
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .match-card {
        padding: 1rem;
    }

    .btn-youtube {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }

    .status-message {
        font-size: 0.8rem;
    }

    .status-timer {
        font-size: 0.9rem;
    }

    .btn-live {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    .live-badge {
        font-size: 0.75rem;
        padding: 0.35rem 0.7rem;
    }
}
