// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Global variables
let currentUser = null;
let userStats = null;
let predictions = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    setupEventListeners();
    if (currentUser) {
        loadUserProfile();
    } else {
        showNoUser();
    }
});

// User management
function initializeUser() {
    const savedUser = localStorage.getItem('valorant_user');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUserDisplay();
    }
}

function updateUserDisplay() {
    if (currentUser) {
        document.getElementById('user-points').textContent = `Points: ${currentUser.points}`;
        document.getElementById('user-id-display').textContent = currentUser.in_game_name || currentUser.full_name;
        document.getElementById('profile-username').textContent = currentUser.in_game_name || currentUser.full_name;
        document.getElementById('profile-user-id').textContent = `Email: ${currentUser.email}`;
    }
}

function showNoUser() {
    document.getElementById('no-user').style.display = 'block';
    document.getElementById('profile-content').style.display = 'none';
}

// Profile management
async function loadUserProfile() {
    try {
        const response = await fetch(`${API_BASE_URL}/user/${currentUser.id}`);
        if (!response.ok) {
            throw new Error('Failed to load user profile');
        }

        const data = await response.json();
        userStats = data.user;
        predictions = data.predictions || [];

        updateProfileStats();
        displayPredictions();
    } catch (error) {
        console.error('Error loading user profile:', error);
        document.getElementById('loading-predictions').innerHTML = 
            '<div class="error">Failed to load profile data. Please try again later.</div>';
    }
}

function updateProfileStats() {
    if (!userStats) return;

    const totalPredictions = predictions.length;
    const correctPredictions = predictions.filter(p => p.is_correct === true).length;
    const accuracy = totalPredictions > 0 ? (correctPredictions / totalPredictions * 100).toFixed(1) : 0;

    document.getElementById('stat-points').textContent = userStats.points || 0;
    document.getElementById('stat-predictions').textContent = totalPredictions;
    document.getElementById('stat-correct').textContent = correctPredictions;
    document.getElementById('stat-accuracy').textContent = `${accuracy}%`;

    // Update current user data
    currentUser.points = userStats.points;
    localStorage.setItem('valorant_user', JSON.stringify(currentUser));
    updateUserDisplay();
}

function displayPredictions() {
    const loadingElement = document.getElementById('loading-predictions');
    const listElement = document.getElementById('predictions-list');
    const noPredictionsElement = document.getElementById('no-predictions');

    loadingElement.style.display = 'none';

    if (predictions.length === 0) {
        noPredictionsElement.style.display = 'block';
        return;
    }

    listElement.innerHTML = '';
    predictions.forEach(prediction => {
        const predictionElement = createPredictionElement(prediction);
        listElement.appendChild(predictionElement);
    });
}

function createPredictionElement(prediction) {
    const element = document.createElement('div');
    element.className = 'prediction-item';

    const match = prediction.match;
    const resultClass = getResultClass(prediction.is_correct);
    const resultText = getResultText(prediction.is_correct, match);

    element.innerHTML = `
        <div class="prediction-match">
            <div class="match-teams">
                <strong>${match.teamA} vs ${match.teamB}</strong>
            </div>
            <div class="prediction-details">
                You predicted: <strong>${prediction.selected_team}</strong>
                ${match.winner ? ` | Winner: <strong>${match.winner}</strong>` : ''}
            </div>
            <div class="prediction-date">
                ${formatDate(prediction.timestamp)}
            </div>
        </div>
        <div class="prediction-result ${resultClass}">
            ${resultText}
        </div>
    `;

    return element;
}

function getResultClass(isCorrect) {
    if (isCorrect === null) return 'result-pending';
    return isCorrect ? 'result-correct' : 'result-incorrect';
}

function getResultText(isCorrect, match) {
    if (isCorrect === null) {
        return match.status === 'finished' ? 'Result pending' : 'Match ongoing';
    }
    return isCorrect ? '✓ Correct (+1 point)' : '✗ Incorrect';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

// Logout functionality
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('valorant_user');
        currentUser = null;
        alert('You have been logged out successfully!');
        window.location.href = 'index.html';
    }
}

// Event listeners
function setupEventListeners() {
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
}
