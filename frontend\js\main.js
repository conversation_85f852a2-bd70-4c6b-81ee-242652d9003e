// Configuration
const API_BASE_URL = 'http://localhost:5000/api'; // Change this to your backend URL

// Global variables
let currentUser = null;
let matches = [];
let countdownIntervals = {};
let globalCountdownInterval = null;
let statusBarVisible = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    setupEventListeners();
    // Always load matches, regardless of login status
    loadMatches();
});

// User management
function initializeUser() {
    const savedUser = localStorage.getItem('valorant_user');
    if (savedUser) {
        try {
            currentUser = JSON.parse(savedUser);
            hideAuthSection();
            updateUserDisplay();
            loadUserStats();
        } catch (e) {
            // Invalid user data, clear and show auth
            localStorage.removeItem('valorant_user');
            showAuthSection();
        }
    } else {
        showAuthSection();
    }
}

function showAuthSection() {
    document.getElementById('auth-section').style.display = 'block';
    // Always show matches section
    document.querySelector('.matches-section').style.display = 'block';
}

function hideAuthSection() {
    document.getElementById('auth-section').style.display = 'none';
    // Always show matches section
    document.querySelector('.matches-section').style.display = 'block';
}

function showMainContent() {
    document.getElementById('auth-section').style.display = 'none';
    document.querySelector('.matches-section').style.display = 'block';
}

function showLoginForm() {
    document.getElementById('login-form').style.display = 'block';
    document.getElementById('register-form').style.display = 'none';
    document.getElementById('auth-title').textContent = 'Welcome Back';
}

function showRegisterForm() {
    document.getElementById('login-form').style.display = 'none';
    document.getElementById('register-form').style.display = 'block';
    document.getElementById('auth-title').textContent = 'Create Account';
}

async function handleLogin() {
    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;

    if (!email || !password) {
        alert('Please enter both email and password');
        return;
    }

    const loginBtn = document.getElementById('login-btn');
    loginBtn.disabled = true;
    loginBtn.textContent = 'Logging in...';

    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            currentUser = data.user;
            localStorage.setItem('valorant_user', JSON.stringify(currentUser));
            hideAuthSection();
            updateUserDisplay();
            loadMatches();
        } else {
            alert(data.error || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('Login failed. Please try again.');
    } finally {
        loginBtn.disabled = false;
        loginBtn.textContent = 'Login';
    }
}

async function handleRegister() {
    const fullName = document.getElementById('register-fullname').value.trim();
    const email = document.getElementById('register-email').value.trim();
    const inGameName = document.getElementById('register-ingame').value.trim();
    const password = document.getElementById('register-password').value;

    if (!fullName || !email || !inGameName || !password) {
        alert('Please fill in all fields');
        return;
    }

    const registerBtn = document.getElementById('register-btn');
    registerBtn.disabled = true;
    registerBtn.textContent = 'Creating Account...';

    try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: fullName,
                email: email,
                in_game_name: inGameName,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            currentUser = data.user;
            localStorage.setItem('valorant_user', JSON.stringify(currentUser));
            hideAuthSection();
            updateUserDisplay();
            loadMatches();
            alert('Account created successfully! Welcome to Valorant Predictions!');
        } else {
            alert(data.error || 'Registration failed');
        }
    } catch (error) {
        console.error('Registration error:', error);
        alert('Registration failed. Please try again.');
    } finally {
        registerBtn.disabled = false;
        registerBtn.textContent = 'Register';
    }
}

function updateUserDisplay() {
    if (currentUser) {
        document.getElementById('user-points').textContent = `Points: ${currentUser.points}`;
        document.getElementById('user-id-display').textContent = currentUser.in_game_name || currentUser.full_name;

        // Show logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'inline-block';
        }
    } else {
        // Hide logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'none';
        }
    }
}

// Load user statistics
async function loadUserStats() {
    if (!currentUser) return;

    try {
        const response = await fetch(`${API_BASE_URL}/user/${currentUser.id}`);
        if (response.ok) {
            const data = await response.json();
            if (data.user) {
                currentUser.points = data.user.points;
                updateUserDisplay();
                // Update localStorage
                localStorage.setItem('valorant_user', JSON.stringify(currentUser));
            }
        }
    } catch (error) {
        console.error('Error loading user stats:', error);
    }
}

// Match management
async function loadMatches() {
    const loadingElement = document.getElementById('loading');
    const matchesContainer = document.getElementById('matches-container');
    const noMatchesElement = document.getElementById('no-matches');

    try {
        if (loadingElement) loadingElement.style.display = 'block';
        if (matchesContainer) matchesContainer.innerHTML = '';
        if (noMatchesElement) noMatchesElement.style.display = 'none';

        console.log('Fetching matches from:', `${API_BASE_URL}/matches`);

        const response = await fetch(`${API_BASE_URL}/matches`);
        console.log('Response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Response error:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('Matches data:', data);
        matches = data.matches || [];

        if (loadingElement) loadingElement.style.display = 'none';

        if (matches.length === 0) {
            if (noMatchesElement) noMatchesElement.style.display = 'block';
            hideGlobalStatusBar(); // Hide status bar if no matches
        } else {
            displayMatches();
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        if (loadingElement) loadingElement.style.display = 'none';
        hideGlobalStatusBar(); // Hide status bar on error

        // Show more detailed error message
        const errorMessage = error.message.includes('fetch')
            ? 'Cannot connect to server. Please check if the backend is running.'
            : `Failed to load matches: ${error.message}`;

        if (matchesContainer) {
            matchesContainer.innerHTML = `<div class="error">${errorMessage}</div>`;
        }
    }
}

function displayMatches() {
    const container = document.getElementById('matches-container');
    container.innerHTML = '';

    matches.forEach(match => {
        const matchCard = createMatchCard(match);
        container.appendChild(matchCard);
    });

    // Update global prediction status bar
    updateGlobalPredictionStatus();
}

function createMatchCard(match) {
    const card = document.createElement('div');
    card.className = 'match-card';
    card.innerHTML = `
        <div class="match-header">
            <span class="match-status status-${match.status}">${match.status}</span>
            <span class="match-id">#${match._id.slice(-6)}</span>
        </div>
        <div class="teams">
            <div class="team">
                <div class="team-name">${match.teamA}</div>
            </div>
            <div class="vs">VS</div>
            <div class="team">
                <div class="team-name">${match.teamB}</div>
            </div>
        </div>
        ${match.status === 'open' ? `
            <div class="countdown" id="countdown-${match._id}"></div>
            <div class="match-actions">
                <button class="btn btn-primary" onclick="openPredictionModal('${match._id}')">
                    <i class="fas fa-trophy"></i> ${currentUser ? 'Make Prediction' : 'Login to Predict'}
                </button>
            </div>
        ` : match.status === 'closed' ? `
            <div class="live-indicator">
                <div class="live-badge">
                    <i class="fas fa-circle"></i>
                    <span>LIVE</span>
                </div>
                <p>Match is currently in progress</p>
            </div>
            <div class="match-actions">
                <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer" class="btn btn-live">
                    <i class="fab fa-youtube"></i>
                    Click to Watch Live
                </a>
            </div>
        ` : match.status === 'finished' && match.winner ? `
            <div class="match-result">
                <strong>Winner: ${match.winner}</strong>
            </div>
        ` : ''}
    `;

    // Start countdown if match is open
    if (match.status === 'open' && match.prediction_end_time) {
        startCountdown(match._id, match.prediction_end_time);
    }

    return card;
}

function startCountdown(matchId, endTime) {
    const countdownElement = document.getElementById(`countdown-${matchId}`);
    if (!countdownElement) return;

    const endDate = new Date(endTime);

    countdownIntervals[matchId] = setInterval(() => {
        const now = new Date();
        const timeLeft = endDate - now;

        if (timeLeft <= 0) {
            countdownElement.textContent = 'Predictions closed';
            clearInterval(countdownIntervals[matchId]);
            // Reload matches to update status
            setTimeout(loadMatches, 1000);
            return;
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// Prediction modal
function openPredictionModal(matchId) {
    if (!currentUser) {
        alert('Please register or login to make predictions!');
        showAuthSection();
        // Scroll to auth section
        document.getElementById('auth-section').scrollIntoView({ behavior: 'smooth' });
        return;
    }

    const match = matches.find(m => m._id === matchId);
    if (!match) return;

    const modal = document.getElementById('prediction-modal');
    const teamAElement = document.getElementById('modal-team-a');
    const teamBElement = document.getElementById('modal-team-b');

    teamAElement.querySelector('.team-name').textContent = match.teamA;
    teamBElement.querySelector('.team-name').textContent = match.teamB;

    // Set up prediction buttons
    teamAElement.querySelector('.predict-btn').onclick = () => makePrediction(matchId, match.teamA);
    teamBElement.querySelector('.predict-btn').onclick = () => makePrediction(matchId, match.teamB);

    // Start modal countdown
    if (match.prediction_end_time) {
        startModalCountdown(match.prediction_end_time);
    }

    modal.style.display = 'block';
}

function startModalCountdown(endTime) {
    const countdownElement = document.getElementById('modal-countdown');
    const endDate = new Date(endTime);

    const updateCountdown = () => {
        const now = new Date();
        const timeLeft = endDate - now;

        if (timeLeft <= 0) {
            countdownElement.textContent = 'Predictions closed';
            return;
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    // Clear interval when modal is closed
    const modal = document.getElementById('prediction-modal');
    const observer = new MutationObserver(() => {
        if (modal.style.display === 'none') {
            clearInterval(interval);
            observer.disconnect();
        }
    });
    observer.observe(modal, { attributes: true, attributeFilter: ['style'] });
}

async function makePrediction(matchId, selectedTeam) {
    if (!currentUser) {
        alert('Please register or login to make predictions!');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/predict`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUser.id,
                match_id: matchId,
                selected_team: selectedTeam
            })
        });

        const data = await response.json();

        if (response.ok) {
            alert(`Prediction submitted successfully! You predicted: ${selectedTeam}`);
            closeModal();
            loadMatches(); // Refresh matches
        } else {
            alert(data.error || 'Failed to submit prediction');
        }
    } catch (error) {
        console.error('Error making prediction:', error);
        alert('Failed to submit prediction. Please try again.');
    }
}

function closeModal() {
    document.getElementById('prediction-modal').style.display = 'none';
}

// Logout functionality
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('valorant_user');
        currentUser = null;
        showAuthSection();
        updateUserDisplay();
        alert('You have been logged out successfully!');
    }
}

// Event listeners
function setupEventListeners() {
    // Authentication event listeners
    const showRegisterBtn = document.getElementById('show-register');
    const showLoginBtn = document.getElementById('show-login');
    const loginBtn = document.getElementById('login-btn');
    const registerBtn = document.getElementById('register-btn');

    if (showRegisterBtn) {
        showRegisterBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showRegisterForm();
        });
    }

    if (showLoginBtn) {
        showLoginBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showLoginForm();
        });
    }

    if (loginBtn) {
        loginBtn.addEventListener('click', handleLogin);
    }

    if (registerBtn) {
        registerBtn.addEventListener('click', handleRegister);
    }

    // Enter key handlers for auth forms
    const loginEmail = document.getElementById('login-email');
    const loginPassword = document.getElementById('login-password');
    if (loginEmail && loginPassword) {
        [loginEmail, loginPassword].forEach(input => {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin();
                }
            });
        });
    }

    const registerInputs = ['register-fullname', 'register-email', 'register-ingame', 'register-password'];
    registerInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleRegister();
                }
            });
        }
    });

    // Modal close
    const closeBtn = document.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    window.addEventListener('click', function(e) {
        const modal = document.getElementById('prediction-modal');
        if (modal && e.target === modal) {
            closeModal();
        }
    });

    // Logout button
    const navLogoutBtn = document.getElementById('nav-logout-btn');
    if (navLogoutBtn) {
        navLogoutBtn.addEventListener('click', logout);
    }

    // Auto-refresh matches every 30 seconds
    setInterval(() => {
        if (currentUser) {
            loadMatches();
        }
    }, 30000);
}

// Global prediction status bar management
function updateGlobalPredictionStatus() {
    const openMatches = matches.filter(match => match.status === 'open' && match.prediction_end_time);

    if (openMatches.length === 0) {
        hideGlobalStatusBar();
        return;
    }

    // Find the earliest prediction deadline
    const earliestMatch = openMatches.reduce((earliest, match) => {
        const matchEndTime = new Date(match.prediction_end_time);
        const earliestEndTime = new Date(earliest.prediction_end_time);
        return matchEndTime < earliestEndTime ? match : earliest;
    });

    showGlobalStatusBar(earliestMatch);
}

function showGlobalStatusBar(match) {
    const statusBar = document.getElementById('prediction-status-bar');
    const mainContent = document.querySelector('.main-content');

    if (!statusBarVisible) {
        statusBar.style.display = 'block';
        mainContent.classList.add('with-status-bar');
        statusBarVisible = true;
    }

    startGlobalCountdown(match.prediction_end_time);
}

function hideGlobalStatusBar() {
    const statusBar = document.getElementById('prediction-status-bar');
    const mainContent = document.querySelector('.main-content');

    if (statusBarVisible) {
        statusBar.style.display = 'none';
        mainContent.classList.remove('with-status-bar');
        statusBarVisible = false;
    }

    if (globalCountdownInterval) {
        clearInterval(globalCountdownInterval);
        globalCountdownInterval = null;
    }
}

function startGlobalCountdown(endTime) {
    const countdownElement = document.getElementById('global-countdown');
    const statusText = document.getElementById('status-text');
    const endDate = new Date(endTime);

    // Clear existing interval
    if (globalCountdownInterval) {
        clearInterval(globalCountdownInterval);
    }

    const updateGlobalCountdown = () => {
        const now = new Date();
        const timeLeft = endDate - now;

        if (timeLeft <= 0) {
            hideGlobalStatusBar();
            // Reload matches to update status
            setTimeout(loadMatches, 1000);
            return;
        }

        const totalMinutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;

        // Format time display
        let timeDisplay;
        if (hours > 0) {
            timeDisplay = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            timeDisplay = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        countdownElement.textContent = timeDisplay;

        // Update status message based on time left
        if (timeLeft <= 300000) { // 5 minutes
            statusText.textContent = 'Prediction window closing very soon!';
            statusText.style.animation = 'pulse 1s infinite';
        } else if (timeLeft <= 900000) { // 15 minutes
            statusText.textContent = 'Prediction window closing soon!';
            statusText.style.animation = 'none';
        } else {
            statusText.textContent = 'Predictions are open!';
            statusText.style.animation = 'none';
        }
    };

    updateGlobalCountdown();
    globalCountdownInterval = setInterval(updateGlobalCountdown, 1000);
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
