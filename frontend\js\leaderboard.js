// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Global variables
let currentUser = null;
let leaderboardData = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    loadLeaderboard();
    setupEventListeners();
});

// User management
function initializeUser() {
    const savedUser = localStorage.getItem('valorant_user');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUserDisplay();
    }
}

function updateUserDisplay() {
    if (currentUser) {
        document.getElementById('user-points').textContent = `Points: ${currentUser.points}`;
        document.getElementById('user-id-display').textContent = currentUser.in_game_name || currentUser.full_name;

        // Show logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'inline-block';
        }
    } else {
        // Hide logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'none';
        }
    }
}

// Leaderboard management
async function loadLeaderboard() {
    const loadingElement = document.getElementById('loading');
    const containerElement = document.getElementById('leaderboard-container');
    const noDataElement = document.getElementById('no-data');

    try {
        loadingElement.style.display = 'block';
        containerElement.style.display = 'none';
        noDataElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/leaderboard`);
        if (!response.ok) {
            throw new Error('Failed to load leaderboard');
        }

        const data = await response.json();
        leaderboardData = data.leaderboard || [];

        loadingElement.style.display = 'none';

        if (leaderboardData.length === 0) {
            noDataElement.style.display = 'block';
        } else {
            containerElement.style.display = 'block';
            displayLeaderboard();
        }
    } catch (error) {
        console.error('Error loading leaderboard:', error);
        loadingElement.style.display = 'none';
        containerElement.innerHTML = '<div class="error">Failed to load leaderboard. Please try again later.</div>';
    }
}

function displayLeaderboard() {
    const listElement = document.getElementById('leaderboard-list');
    listElement.innerHTML = '';

    leaderboardData.forEach((user, index) => {
        const entry = createLeaderboardEntry(user, index + 1);
        listElement.appendChild(entry);
    });
}

function createLeaderboardEntry(user, rank) {
    const entry = document.createElement('div');
    entry.className = 'table-row';
    
    // Highlight current user
    if (currentUser && user._id === currentUser.id) {
        entry.classList.add('current-user');
    }

    // Add special styling for top 3
    if (rank <= 3) {
        entry.classList.add(`rank-${rank}`);
    }

    const rankIcon = getRankIcon(rank);
    const accuracy = user.total_predictions > 0 ? user.accuracy.toFixed(1) : '0.0';

    entry.innerHTML = `
        <div class="rank">
            ${rankIcon}
            <span class="rank-number">${rank}</span>
        </div>
        <div class="username">
            ${user.display_name || user.in_game_name || user.full_name || `User ${user._id.slice(-8)}`}
            ${currentUser && user._id === currentUser.id ? '<span class="you-badge">YOU</span>' : ''}
        </div>
        <div class="points">
            <strong>${user.points}</strong>
        </div>
        <div class="predictions">
            ${user.total_predictions}
        </div>
        <div class="accuracy">
            ${accuracy}%
        </div>
    `;

    return entry;
}

function getRankIcon(rank) {
    switch (rank) {
        case 1:
            return '<i class="fas fa-crown gold"></i>';
        case 2:
            return '<i class="fas fa-medal silver"></i>';
        case 3:
            return '<i class="fas fa-medal bronze"></i>';
        default:
            return '';
    }
}

// Logout functionality
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('valorant_user');
        currentUser = null;
        updateUserDisplay();
        alert('You have been logged out successfully!');
        window.location.href = 'index.html';
    }
}

// Event listeners
function setupEventListeners() {
    document.getElementById('refresh-btn').addEventListener('click', loadLeaderboard);

    // Logout button
    const navLogoutBtn = document.getElementById('nav-logout-btn');
    if (navLogoutBtn) {
        navLogoutBtn.addEventListener('click', logout);
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');

    if (mobileMenuToggle && mobileNav) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }

    // Close mobile menu when clicking on nav links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (mobileNav && mobileNav.classList.contains('active')) {
            if (!mobileNav.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                closeMobileMenu();
            }
        }
    });

    // Auto-refresh every 60 seconds
    setInterval(loadLeaderboard, 60000);
}

// Mobile menu functions
function toggleMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');

    if (mobileMenuToggle && mobileNav) {
        mobileMenuToggle.classList.toggle('active');
        mobileNav.classList.toggle('active');

        // Prevent body scroll when menu is open
        if (mobileNav.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    }
}

function closeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');

    if (mobileMenuToggle && mobileNav) {
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        document.body.style.overflow = '';
    }
}
